FROM python:3.11-slim-bookworm

ARG NRU=storm-etl
ARG NRUG=docker-users
ARG CODE_ARTIFACT_TOKEN

ENV HOME=/home/<USER>
ENV VIRTUAL_ENV=$HOME/venv
ENV PATH=$VIRTUAL_ENV/bin:$PATH

ARG SED_FILE_PARSE_REGEX='/^\s*#/d;/^\s*$/d;s/[\r\n]//g'

ARG BUILD_CONFIG_DIR=config
ARG BUILD_SOURCE_DIR=src

WORKDIR /root

# Install OS packages
# --------------------------------------------------
COPY $BUILD_CONFIG_DIR/packages-os.conf packages-os.conf

# RUN echo "deb http://security.debian.org/debian-security bookworm-security main" >> /etc/apt/sources.list

# Install required packages (curl, unzip, wget, dpkg)
RUN apt-get update && \
  apt-get install -y --no-install-recommends \
    curl \
    unzip \
    wget \
    dpkg \
    unixodbc \
    unixodbc-dev \
    libsasl2-modules-gssapi-mit && \
    # apt-get install -y libtasn1-6=4.19.0-2+deb12u1 && \
    # apt-mark hold libtasn1-6 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Ensure the package list is updated and all vulnerable packages are upgraded
RUN apt-get update && apt-get upgrade -y

# Install curl and related libraries explicitly
RUN apt-get install -y --only-upgrade \
    libcurl3-gnutls \
    libcurl4 \
    curl \
    libkrb5-3 \
    libgssapi-krb5-2 \
    libk5crypto3 \
    libkrb5support0 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install AWS CLI
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
  unzip awscliv2.zip && \
  ./aws/install && \
  rm -rf aws awscliv2.zip

# Upgrade vulnerable Python packages
RUN python -m pip uninstall -y setuptools certifi \
  && python -m pip install setuptools==80.9.0 certifi==2024.07.04

RUN apt-get update && apt-get install -y \
  pkg-config \
  libssl-dev \
  libpsl-dev \
  build-essential \
  && rm -rf /var/lib/apt/lists/*

RUN curl -LO https://ftp.gnu.org/gnu/wget/wget-1.25.0.tar.gz \
  && tar -xzvf wget-1.25.0.tar.gz \
  && cd wget-1.25.0 \
  && ./configure --with-ssl=openssl \
  && make \
  && make install \
  && cd .. \
  && rm -rf wget-1.25.0 wget-1.25.0.tar.gz
# Official Debian and Ubuntu images automatically run apt-get clean,
# so explicit invocation is not required.

# Install Simba ODBC Driver
RUN wget https://databricks-bi-artifacts.s3.us-east-2.amazonaws.com/simbaspark-drivers/odbc/2.6.26/SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && unzip SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && dpkg -i simbaspark_2.6.26.1045-2_amd64.deb \
  && rm -f SimbaSparkODBC-2.6.26.1045-2_amd64.deb

COPY $BUILD_CONFIG_DIR/odbc.ini /etc/odbc.ini
RUN chmod 777 /etc/odbc.ini
COPY $BUILD_CONFIG_DIR/odbcinst.ini /etc/odbcinst.ini

# Configure non-root user, group, and home directory
# --------------------------------------------------
RUN groupadd -g 999 $NRUG \
  && useradd -r -u 999 -g $NRUG $NRU

RUN mkdir -m 755 -p $HOME \
  && chown $NRU:$NRUG $HOME

# Switch to NRU and set working directory
# ---------------------------------------
USER $NRU
WORKDIR $HOME

# Configure python VENV with proper libraries
# -------------------------------------------
RUN python -m venv $VIRTUAL_ENV \
  && python -m pip install --upgrade pip setuptools==80.9.0

COPY --chown=$NRU:$NRUG $BUILD_CONFIG_DIR/packages-py.conf $BUILD_CONFIG_DIR/packages-py.conf

# Use aws artifact for packages
RUN pip config set global.extra-index-url https://aws:$<EMAIL>/pypi/pypi/simple/

# Install Python packages
RUN pip install $(sed $SED_FILE_PARSE_REGEX $BUILD_CONFIG_DIR/packages-py.conf)

# Verify curl installation as non-root user
RUN curl --version

# Copy in source code
# -------------------------------------------
COPY --chown=$NRU:$NRUG $BUILD_SOURCE_DIR/main.py $BUILD_SOURCE_DIR/main.py

# Run the script
CMD ["python", "src/main.py"]