#####################################
# Python Package Configuration file #
#####################################

# Python packages are installed via pip. To add a new package, enter the package name on a new
# line at the approriate location (packages are sorted alphabetically).
esource_etr_writer==3.8.5
pandas
pymongo>=4.8.0
pyodbc==5.1.0
requests
setuptools>=70.0
boto3>=1.26.73
databricks-sql-connector