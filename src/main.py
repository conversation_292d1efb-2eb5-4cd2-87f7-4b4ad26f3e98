from datetime import datetime
import logging
import os
import sys

from pymongo import MongoClient
import pandas as pd
import pyodbc
import requests
import time

from databricks import sql

# --------------------
# ENVIRONMENT
# --------------------

# Logging Setup
logging.basicConfig(format="%(asctime)s %(levelname)s [%(name)s] %(message)s")
logging.getLogger().setLevel(os.environ.get("LOG_LEVEL", logging.INFO))

# Client Auth
CLIENT_AUTHORIZATION = os.getenv("CLIENT_AUTHORIZATION")

# Mongo Creds
MONGO_USER = os.getenv("MONGO_USER")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")
MONGO_HOST = os.getenv("MONGO_HOST")

REGION_MAPPER = os.getenv("REGION_MAPPER", "parent_zone")

# Auth Control Flow
# --------------------
# SERVICE PRINCIPAL
# --------------------
ACF_TENANT_ID = os.getenv("ACF_TENANT_ID")  # ap-ramp-XX-svcpri-etl.tenant
ACF_CLIENT_ID = os.getenv("ACF_CLIENT_ID")  # ap-ramp-XX-svcpri-etl.appId
ACF_CLIENT_SECRET = os.getenv("ACF_CLIENT_SECRET")  # ap-ramp-XX-svcpri-etl.password
ACF_SCOPE = os.getenv("ACF_SCOPE")  # DBX Service Scope

# Databricks
DBS_HOST = os.getenv("DBS_HOST")
DBS_PORT = os.getenv("DBS_PORT")
DBS_HTTP_PATH = os.getenv("DBS_HTTP_PATH")

# Queries
DBS_CATALOG = os.getenv("DBS_CATALOG")
DBS_WEATHER_CATALOG = os.getenv("DBS_WEATHER_CATALOG", os.getenv("DBS_CATALOG"))
DBS_SCHEMA = os.getenv("DBS_SCHEMA")
DBS_WEATHER_TABLE = os.getenv("DBS_WEATHER_TABLE")
DBS_STATION_TABLE = os.getenv("DBS_STATION_TABLE")

DBS_WEATHER_STATION_SCHEMA = os.getenv("DBS_WEATHER_STATION_SCHEMA")

DBS_BACKPOPULATION_SCHEMA = os.getenv(
    "DBS_BACKPOPULATION_SCHEMA", os.getenv("DBS_SCHEMA")
)
DBS_BACKPOPULATION_WEATHER_TABLE = os.getenv(
    "DBS_BACKPOPULATION_WEATHER_TABLE", os.getenv("DBS_WEATHER_TABLE")
)

AWS_ACCESS_TOKEN = os.getenv("AWS_ACCESS_TOKEN")


# --------------------
# Classes
# --------------------
class TokenRetrievalFailure(Exception):
    pass


# --------------------
# Functions
# --------------------

def retrieve_access_token() -> str:
    url = f"https://login.microsoftonline.com/{ACF_TENANT_ID}/oauth2/v2.0/token"
    data = {
        "client_id": ACF_CLIENT_ID,
        "client_secret": ACF_CLIENT_SECRET,
        "grant_type": "client_credentials",
        "scope": ACF_SCOPE,
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()
        content = response.json()
        return content["access_token"]
    except requests.exceptions.RequestException as e:
        logging.error(f"Network error occurred: {e}")
        raise e
    except (TokenRetrievalFailure, ValueError, KeyError) as e:
        logging.error(f"Error retrieving access token: {e}")
        raise e


def dbs_conn_string_azure(access_token: str) -> str:
    return (
        f"host={DBS_HOST};"
        + f"port={DBS_PORT};"
        + f"httppath={DBS_HTTP_PATH};"
        + f"auth_accesstoken={access_token};"
        + "thrifttransport=2;"
        + "ssl=1;"
        + "authmech=11;"
        + "auth_flow=0;"
        + "driver=/opt/simba/spark/lib/64/libsparkodbc_sb64.so;"
    )


def df_size(df: pd.DataFrame):
    return df.shape[0]


def run_dbs_query(cursor, query: str):
    df = pd.DataFrame()

    try:
        logging.info(f"Running dbx query: '{query}'...")
        cursor.execute(query)
        df = pd.DataFrame.from_records(
            cursor.fetchall(), columns=[col[0] for col in cursor.description]
        )

        if not df.empty:
            logging.info(f"Query returned {df_size(df)} record(s).")
        else:
            logging.warning("Query returned no results.")

    except Exception as e:
        logging.error(e)
        raise e

    return df


def convert_to_titlecase(value):
    if value is None:
        return ""

    value = value.replace("PD - ", "")

    # Convert to title case while preserving spaces
    titlecase_value = " ".join(word.capitalize() for word in value.split())

    return titlecase_value


def append_weather_data_regions(df_latest_weather, df_static_stations):
    weather_data = []
    for _, row in df_latest_weather.iterrows():
        station_uuid = row["weather_station_uuid"]
        station_data = df_static_stations[
            df_static_stations["weather_station_uuid"] == station_uuid
        ].iloc[0]
        #### This should be in the Writer/Reader
        weather_station_data = {
            "stationId": station_uuid,
            "modelZone" : station_data["model_zone"],
            "latitude": station_data["latitude"],
            "longitude": station_data["longitude"],
            "region": convert_to_titlecase(station_data[REGION_MAPPER]),
            "workGroup": convert_to_titlecase(station_data["zone"]),
            "parentZone" : convert_to_titlecase(station_data["parent_zone"]),
            "zone" : station_data["zone"],
            "weatherAttributes": {
                "temperatureCelsius": row["temperature_celsius"],
                "dewPointCelsius": row["dew_point_celsius"],
                "humidityPercent": row["humidity_percent"],
                "windBearingDeg": row["wind_bearing_deg"],
                "windGustKmh": row["gust_kmh"],
                "windGustMph": row["gust_kmh"] * 0.621371,
                "windSpeedKmh": row.get("wind_speed_kmh", 0),
                "windSpeedMph": row.get("wind_speed_kmh", 0) * 0.621371,
                "apparentTemperatureCelsius": row["apparent_temperature_celsius"],
                "iceCoverPercent": row["ice_cover_percent"],
                "precipitationType": row["precipitation_type"],
                "precipitationAmountMm": row["precipitation_amount_mm"],
                "visibilityKm": row["visibility_km"],
                "cloudCoverPercent": row["cloud_cover_percent"],
                "meanSeaLevelPressureHpamb": row["mean_sea_level_pressure_hpamb"],
                "longWaveUpwardSolarFluxWm2": row["long_wave_upward_solar_flux_wm2"],
                "shortWaveDownwardSolarFluxWm2": row["short_wave_downward_solar_flux_wm2"],
                "ozoneDobson": row["ozone_dobson"],
                "severeWeatherOutlook": row["severe_weather_outlook"],
            },
        }
        #### End of Writer/Reader document
        weather_data.append(weather_station_data)
    return weather_data


def insert_weather_data_into_mongo(weather_data, forecast_datetime):
    client = MongoClient(
        host=MONGO_HOST,
        port=27017,
        username=MONGO_USER,
        password=MONGO_PASSWORD,
        authSource="admin",
        authMechanism="SCRAM-SHA-256",
    )
    db = client[os.getenv("CLIENT_DB", "storm_metrics")]
    collection = db[os.getenv("WEATHER_COLLECTION", "weather")]

    if isinstance(forecast_datetime, str):
        forecast_datetime = datetime.strptime(forecast_datetime, "%Y-%m-%d %H:%M:%S")

    forecast_datetime = forecast_datetime.replace(second=0, microsecond=0)

    document = {
        "timestamp": forecast_datetime,
        "weatherStations": {d["stationId"]: d for d in weather_data},
        "weatherStationMetadata": {"isMock": None},
    }

    collection.delete_many({"timestamp": forecast_datetime})
    
    collection.insert_one(document)
    
    logging.info(
        f"Weather data for {forecast_datetime} refreshed in MongoDB successfully."
    )


def run_weather_data_query():
    weather_query = f"""
    SELECT *
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_WEATHER_TABLE}
      WHERE forecast_datetime = (
          SELECT MAX(forecast_datetime)
          FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_WEATHER_TABLE}
      )
    """

    station_query = f"""
    SELECT *
    FROM {DBS_WEATHER_CATALOG}.{DBS_WEATHER_STATION_SCHEMA}.{DBS_STATION_TABLE}
    """

    logging.info("Running query for weather data...")
    try:
        df_latest_weather = run_dbs_query(cursor, weather_query)
        df_static_stations = run_dbs_query(cursor, station_query)

        if df_latest_weather.empty:
            logging.error("The Weather Data DataFrame is empty or not found.")
        elif df_static_stations.empty:
            logging.error("The Station Data DataFrame is empty or not found.")
        else:
            forecast_datetime = df_latest_weather["forecast_datetime"].iloc[0]
            weather_data = append_weather_data_regions(
                df_latest_weather, df_static_stations
            )
            insert_weather_data_into_mongo(weather_data, forecast_datetime)
    except Exception as e:
        logging.error(
            f"Error running new query on {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_WEATHER_TABLE}: {e}"
        )
        raise e


def run_backpopulation():
    logging.info("Running backpopulation queries...")
    
    # Query to retrieve all weather data
    weather_data_query = f"""
    SELECT *
    FROM {DBS_CATALOG}.{DBS_BACKPOPULATION_SCHEMA}.{DBS_BACKPOPULATION_WEATHER_TABLE}
    """

    # Query to retrieve all static station data
    station_query = f"""
    SELECT *
    FROM {DBS_WEATHER_CATALOG}.{DBS_WEATHER_STATION_SCHEMA}.{DBS_STATION_TABLE}
    """

    logging.info("Running queries to retrieve all weather data and station data...")
    try:
        # Load all weather data into memory
        df_all_weather_data = run_dbs_query(cursor, weather_data_query)

        # Load all static station data into memory
        df_static_stations = run_dbs_query(cursor, station_query)

        if df_all_weather_data.empty:
            logging.error("No weather data found.")
        elif df_static_stations.empty:
            logging.error("The Station Data DataFrame is empty or not found.")
        else:
            # Process the data in memory
            distinct_dates = df_all_weather_data["forecast_datetime"].unique()

            for forecast_datetime in distinct_dates:
                logging.info(
                    f"Processing data for forecast_datetime: {forecast_datetime}"
                )

                df_latest_weather = df_all_weather_data[
                    df_all_weather_data["forecast_datetime"] == forecast_datetime
                ]

                weather_data = append_weather_data_regions(
                    df_latest_weather, df_static_stations
                )
                insert_weather_data_into_mongo(weather_data, forecast_datetime)

    except Exception as e:
        logging.error(f"Error during backpopulation: {e}")
        raise e


def run_backpopulation_or_query():
    if os.getenv("BACKPOPULATE", "false").lower() == "true":
        logging.info("Running backpopulation of weather data...")
        run_backpopulation()
    else:
        logging.info("Running Weather Data Query...")
        run_weather_data_query()


def run():
    while True:
        try:
            time.sleep(60)  # Simulate some work

        except Exception as e:
            logging.error(f"An error occurred: {e}")


## Interactive pod work ONLY! ##
# run()

##################
### Main block ###
##################

if CLIENT_AUTHORIZATION == "azure":
    try:
        access_token = retrieve_access_token()
        logging.info("Access token retrieved successfully.")
    except Exception as e:
        logging.error(f"Could not retrieve token: {e}")
        raise e

    logging.info("Connecting to Databricks...")
    conn = pyodbc.connect(dbs_conn_string_azure(access_token), autocommit=True)
    cursor = conn.cursor()
    logging.info("Databricks connection complete...")

    run_backpopulation_or_query()

if CLIENT_AUTHORIZATION == "aws":
    logging.info("Connecting to Databricks in AWS...")

    try:
        with sql.connect(
            server_hostname=DBS_HOST,
            http_path=DBS_HTTP_PATH,
            access_token=AWS_ACCESS_TOKEN,
        ) as conn:
            logging.info("Connection to AWS Databricks successful!")

            with conn.cursor() as cursor:
                run_backpopulation_or_query()

    except Exception as e:
        logging.error(f"Error connecting to AWS Databricks: {e}")
        raise e
