pipeline {
  agent {
    kubernetes {
      inheritFrom 'default-agent'
      yaml """
      apiVersion: v1
      kind: Pod
      spec:
        containers:
        - name: aws-cli
          image: amazon/aws-cli:latest
          command: ['sleep', '99d'] 
        - name: docker-client
          image: docker:27
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
        - name: docker-daemon
          image: docker:27-dind
          env:
            - name: DOCKER_TLS_CERTDIR
              value: ""
          securityContext:
            privileged: true
          volumeMounts:
              - name: cache
                mountPath: /var/lib/docker
        volumes:
          - name: cache
            emptyDir: {}
      """
    }
  }
  parameters {
    string(name: 'IMAGE_NAME', defaultValue: 'esource/storm-databricks-weather-ingest')
    string(name: 'VERSION',    defaultValue: 'latest')
  }
  stages {
    stage('Create canonical version') {
      steps {
        script {
          env.VERSION = env.BRANCH_NAME.replaceAll("/", "_")
        }
      }
    }
    stage('Login to AWS Codeartifact') {
      steps {
        script {
          container("aws-cli"){
            env.CODE_ARTIFACT_TOKEN = sh(script: 'aws codeartifact get-authorization-token --domain esource-int-artifacts --domain-owner 411985166407 --region us-east-1 --query authorizationToken --output text', returnStdout: true)
          }
        }
      }
    }
    stage('Build Docker Image') {
      steps {
        container('docker-client') {
          sleep 15 // wait for docker daemon to start
          sh "docker build . -t $IMAGE_NAME:$VERSION --build-arg CODE_ARTIFACT_TOKEN=${env.CODE_ARTIFACT_TOKEN}"
        }
      }
    }
    stage('Push Docker Image to AWS ECR') {
      when {
        anyOf {
          branch 'main'
          branch 'staging'
          branch 'development'
          branch 'internal'
          branch 'interactive'
        }
      }
      steps {
        script {
          docker_pwd = ''
          imageName = "${env.IMAGE_NAME}:${env.VERSION}"
          repository = '411985166407.dkr.ecr.us-east-1.amazonaws.com'
          container('aws-cli') {
            docker_pwd = sh(script: "aws ecr get-login-password --region us-east-1", returnStdout: true)
          }
          container('docker-client') {
            sh "echo \"${docker_pwd}\" | docker login -u AWS --password-stdin ${repository}"
            sh "docker tag ${imageName} ${repository}/${imageName}"
            sh "docker push ${repository}/${imageName}"
          }
        }
      }
    }
    stage('Deploy: APC Azure CR (DV)') {
      when { branch 'development' }
      steps {
        container('docker-client') {
          script {
            dv_repository = "aprampdvacr.azurecr.io" //change this?
            withCredentials([usernamePassword(credentialsId: 'apc_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${dv_repository}"
            }
            sh "docker tag $IMAGE_NAME:$VERSION ${dv_repository}/$IMAGE_NAME:$VERSION"
            sh "docker push ${dv_repository}/$IMAGE_NAME:$VERSION"
            sh "docker tag $IMAGE_NAME:$VERSION ${dv_repository}/$IMAGE_NAME:latest"
            sh "docker push ${dv_repository}/$IMAGE_NAME:latest"
          }
        }
      }
    }
    stage('Deploy: APC Azure CR (UA)') {
      when { branch 'staging' }
      steps {
        container('docker-client') {
          script {
            ua_repository = "aprampuaacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_ua_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${ua_repository}"
            }
            sh "docker tag $IMAGE_NAME:$VERSION ${ua_repository}/$IMAGE_NAME:$VERSION"
            sh "docker push ${ua_repository}/$IMAGE_NAME:$VERSION"
            sh "docker tag $IMAGE_NAME:$VERSION ${ua_repository}/$IMAGE_NAME:latest"
            sh "docker push ${ua_repository}/$IMAGE_NAME:latest"
          }
        }
      }
    }
  }
}
